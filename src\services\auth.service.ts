import axiosPrivate from "@/config/api";
import { authEndPoint } from "@/config/endpoint";

export interface LoginCredentials {
  name: string;
  password: string;
}

export interface RegisterData {
  name: string;
  password: string;
  phoneNumber: string;
  userName: string;
  avatarUrl?: string;
}

export interface OtpSendData {
  phone: string;
}

export interface OtpVerifyData {
  phoneNumber: string;
  code: string;
  isSignUp?: boolean;
}

export interface AuthResponse {
  accessToken?: string;
  refreshToken?: string;
  message?: string;
  userExists?: boolean;
  [key: string]: any; // For additional properties that might be returned
}

export const authService = {
  // Login with username and password
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await axiosPrivate.post(authEndPoint.login, credentials);
    return response.data;
  },

  // Register new user
  register: async (data: RegisterData): Promise<AuthResponse> => {
    const response = await axiosPrivate.post(authEndPoint.register, data);
    return response.data;
  },

  // Send OTP to phone number
  sendOtp: async (data: OtpSendData): Promise<void> => {
    await axiosPrivate.post(authEndPoint.otpSend, data);
  },

  // Verify OTP code
  verifyOtp: async (data: OtpVerifyData): Promise<AuthResponse> => {
    const response = await axiosPrivate.post(authEndPoint.otpVerify, data);
    return response.data;
  },

  // Get current user info
  getMe: async () => {
    const response = await axiosPrivate.get(authEndPoint.me);
    return response.data;
  },

  // Helper function to format phone number
  formatPhoneNumber: (phone: string): string => {
    // Remove spaces and + sign, format phone number
    const cleanPhone = phone.replace(/[\s+]/g, "");
    let phoneWithPrefix = cleanPhone;

    if (phoneWithPrefix.startsWith("+998")) {
      phoneWithPrefix = phoneWithPrefix.substring(1);
    } else if (phoneWithPrefix.startsWith("998")) {
      phoneWithPrefix = phoneWithPrefix;
    } else {
      phoneWithPrefix = `998${phoneWithPrefix}`;
    }

    return phoneWithPrefix;
  },
};
