import axiosPrivate from "@/config/api";
import { authEndPoint } from "@/config/endpoint";
import { toast } from "@/utils/toast";

export interface LoginCredentials {
  name: string;
  password: string;
}

export interface RegisterData {
  name: string;
  password: string;
  phoneNumber: string;
  userName: string;
  avatarUrl?: string;
}

export interface OtpSendData {
  phone: string;
}

export interface OtpVerifyData {
  phoneNumber: string;
  code: string;
  isSignUp?: boolean;
}

export interface AuthResponse {
  accessToken?: string;
  refreshToken?: string;
  message?: string;
  userExists?: boolean;
  [key: string]: any; // For additional properties that might be returned
}

export interface AuthResult {
  success: boolean;
  shouldNavigate?: boolean;
  shouldRedirectToLogin?: boolean;
  message?: string;
}

export const authService = {
  // Helper function to format phone number
  formatPhoneNumber: (phone: string): string => {
    // Remove spaces and + sign, format phone number
    const cleanPhone = phone.replace(/[\s+]/g, "");
    let phoneWithPrefix = cleanPhone;

    if (phoneWithPrefix.startsWith("+998")) {
      phoneWithPrefix = phoneWithPrefix.substring(1);
    } else if (phoneWithPrefix.startsWith("998")) {
      phoneWithPrefix = phoneWithPrefix;
    } else {
      phoneWithPrefix = `998${phoneWithPrefix}`;
    }

    return phoneWithPrefix;
  },

  // Helper function to store tokens
  storeTokens: (accessToken: string, refreshToken?: string): void => {
    localStorage.setItem("accessToken", accessToken);
    if (refreshToken) {
      localStorage.setItem("refreshToken", refreshToken);
    }
  },

  // Send OTP to phone number
  sendOtpRequest: async (phone: string): Promise<AuthResult> => {
    try {
      const phoneWithPrefix = authService.formatPhoneNumber(phone);
      await axiosPrivate.post(authEndPoint.otpSend, { phone: phoneWithPrefix });
      toast.success("OTP kodu gönderildi");
      return { success: true };
    } catch (error: any) {
      toast.error(error.response?.data?.message || "OTP gönderilemedi");
      return { success: false };
    }
  },

  // Login with username and password - complete flow
  loginWithCredentials: async (
    credentials: LoginCredentials,
    navigate: (path: string, options?: any) => void
  ): Promise<AuthResult> => {
    try {
      const response = await axiosPrivate.post(authEndPoint.login, credentials);

      if (response.data.accessToken) {
        authService.storeTokens(
          response.data.accessToken,
          response.data.refreshToken
        );
        toast.success("Giriş başarılı");
        navigate("/", { replace: true });

        // Force page reload to update navbar and fetch user data
        setTimeout(() => {
          window.location.reload();
        }, 100);

        return { success: true, shouldNavigate: true };
      } else {
        toast.error("Giriş başarısız");
        return { success: false };
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Giriş başarısız");
      return { success: false };
    }
  },

  // Verify OTP for login - complete flow
  verifyOtpForLogin: async (
    phone: string,
    code: string,
    navigate: (path: string, options?: any) => void
  ): Promise<AuthResult> => {
    try {
      const phoneWithPrefix = authService.formatPhoneNumber(phone);
      const response = await axiosPrivate.post(authEndPoint.otpVerify, {
        phoneNumber: phoneWithPrefix,
        code: code,
      });

      if (response.data.accessToken) {
        authService.storeTokens(
          response.data.accessToken,
          response.data.refreshToken
        );
        toast.success("Giriş başarılı");
        navigate("/", { replace: true });

        // Force page reload to update navbar and fetch user data
        setTimeout(() => {
          window.location.reload();
        }, 100);

        return { success: true, shouldNavigate: true };
      } else if (response.data.message || response.status === 200) {
        toast.success("Giriş başarılı");
        navigate("/", { replace: true });

        // Force page reload to update navbar and fetch user data
        setTimeout(() => {
          window.location.reload();
        }, 100);

        return { success: true, shouldNavigate: true };
      }
      return { success: false };
    } catch (error: any) {
      toast.error(error.response?.data?.message || "OTP doğrulanamadı");
      return { success: false };
    }
  },

  // Verify OTP for signup - complete flow
  verifyOtpForSignup: async (
    phone: string,
    code: string,
    navigate: (path: string, options?: any) => void
  ): Promise<AuthResult & { phoneNumber?: string }> => {
    try {
      const phoneWithPrefix = authService.formatPhoneNumber(phone);
      const response = await axiosPrivate.post(authEndPoint.otpVerify, {
        phoneNumber: phoneWithPrefix,
        code: code,
        isSignUp: true,
      });

      // Check if the response contains an access token (user already exists)
      if (response.data.accessToken) {
        authService.storeTokens(
          response.data.accessToken,
          response.data.refreshToken
        );
        toast.success("Giriş başarılı! Kullanıcı zaten mevcut.");
        navigate("/", { replace: true });

        // Force page reload to update navbar and fetch user data
        setTimeout(() => {
          window.location.reload();
        }, 100);

        return { success: true, shouldNavigate: true };
      }

      // Check if user exists but needs login
      if (
        response.data.userExists ||
        response.data.message?.includes("kullanıcı mevcut")
      ) {
        toast.info(
          "Bu telefon numarası kayıtlı. Giriş sayfasına yönlendiriliyorsunuz."
        );
        navigate("/login", { replace: true });
        return { success: true, shouldRedirectToLogin: true };
      }

      // OTP verified but user doesn't exist, proceed to registration
      if (
        response.data.message === "Kod muvaffaqiyatli tasdiqlandi" ||
        response.data.message?.includes("tasdiqlandi")
      ) {
        toast.success("OTP doğrulandı - Kayıt formunu doldurun");
        return { success: true, phoneNumber: phoneWithPrefix };
      } else {
        toast.error("OTP doğrulanamadı");
        return { success: false };
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "OTP doğrulanamadı");
      return { success: false };
    }
  },

  // Register user - complete flow
  registerUser: async (
    data: RegisterData,
    navigate: (path: string, options?: any) => void
  ): Promise<AuthResult> => {
    try {
      const response = await axiosPrivate.post(authEndPoint.register, data);

      if (response.data.accessToken) {
        authService.storeTokens(
          response.data.accessToken,
          response.data.refreshToken
        );

        // Get user data after successful registration
        try {
          const userResponse = await axiosPrivate.get(authEndPoint.me);
          if (userResponse.data) {
            toast.success(
              `Kayıt başarılı! Hoş geldiniz ${userResponse.data.name}!`
            );
            navigate("/", { replace: true });

            // Force page reload to update navbar and fetch user data
            setTimeout(() => {
              window.location.reload();
            }, 100);

            return { success: true, shouldNavigate: true };
          }
        } catch (userError) {
          // If getting user data fails, still redirect but with generic message
          toast.success("Kayıt başarılı! Hoş geldiniz!");
          navigate("/", { replace: true });

          // Force page reload to update navbar
          setTimeout(() => {
            window.location.reload();
          }, 100);

          return { success: true, shouldNavigate: true };
        }
      } else {
        toast.success("Kayıt başarılı");
        navigate("/", { replace: true });
        return { success: true, shouldNavigate: true };
      }
      return { success: false };
    } catch (error: any) {
      console.error("Registration error:", error);
      toast.error(error.response?.data?.message || "Kayıt başarısız");
      return { success: false };
    }
  },
};

// Auth service for handling authentication flows
